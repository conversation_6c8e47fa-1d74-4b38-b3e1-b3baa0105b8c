# Instagram Video Ads Module - Jednoduchá verze

Jednoduchý JavaScriptový modul, kter<PERSON> najde Instagram příspěvky na stránce, př<PERSON><PERSON><PERSON> je layerem s Play tlačítkem a po kliknutí přehraje video reklamu.

## Jak to funguje

1. **Najde Instagram embedy** - automaticky detekuje `blockquote.instagram-media` elementy
2. **P<PERSON><PERSON><PERSON> overlay** - p<PERSON><PERSON><PERSON><PERSON> embed poloprůhlednou vrstvou s Play tlačítkem
3. **Přehraje reklamu** - po kliknutí spustí VAST video reklamu pomocí Google IMA SDK
4. **Odstraní vše** - po skončení reklamy se overlay i přehrávač úplně odstraní

## Funkcionality

- ✅ Automatická detekce Instagram embedů
- ✅ Instagram-styled Play tlačítko s gradientem
- ✅ Přesné př<PERSON> embedu (<PERSON><PERSON><PERSON><PERSON> p<PERSON>)
- ✅ Google IMA SDK integrace pro VAST reklamy
- ✅ Úplné odstranění po skončení reklamy
- ✅ Podpora více embedů na stránce

## Instalace a použití

### 1. Základní implementace

```html
<!DOCTYPE html>
<html>
<head>
    <title>Instagram Video Ads</title>
</head>
<body>
    <!-- Váš Instagram embed -->
    <blockquote class="instagram-media" data-instgrm-permalink="...">
        <!-- Instagram obsah -->
    </blockquote>

    <!-- Povinné skripty -->
    <script async src="//www.instagram.com/embed.js"></script>
    <script src="//imasdk.googleapis.com/js/sdkloader/ima3.js"></script>
    <script src="instagram-video-ads.js"></script>
</body>
</html>
```

### 2. Konfigurace VAST URL

Pro změnu VAST tag URL upravte konstantu v souboru `instagram-video-ads.js`:

```javascript
const CONFIG = {
    VAST_TAG_URL: 'https://your-vast-server.com/vast-tag-url'
};
```

### 3. Testování

Otevřete `index.html` v prohlížeči - obsahuje skutečný Instagram embed pro testování.

## Technické detaily

### Jak modul funguje

1. **Detekce**: Každé 3 sekundy hledá `blockquote.instagram-media` elementy
2. **Overlay**: Přidá poloprůhlednou vrstvu s Instagram-styled Play tlačítkem
3. **Reklama**: Po kliknutí vytvoří video kontejner a spustí VAST reklamu
4. **Cleanup**: Po skončení odstraní overlay i video kontejner

### Klíčové funkce

- `findAndProcessInstagramEmbeds()` - najde a zpracuje embedy
- `addOverlayToEmbed()` - přidá overlay s Play tlačítkem
- `playVideoAd()` - spustí video reklamu pomocí Google IMA SDK
- `cleanupAd()` - odstraní vše po skončení reklamy

## Debugging

Modul poskytuje debugging rozhraní:

```javascript
// Zobrazení zpracovaných embedů
console.log(window.InstagramVideoAds.getProcessedEmbeds());

// Zobrazení konfigurace
console.log(window.InstagramVideoAds.getConfig());

// Reinicializace modulu
window.InstagramVideoAds.reinitialize();
```

## Požadavky

- Google IMA SDK v3
- Instagram embed.js
- Moderní prohlížeč s podporou ES6+

## Řešení problémů

### Časté problémy

1. **Overlay se nezobrazí**
   - Zkontrolujte konzoli prohlížeče na chyby
   - Ověřte, že Instagram embed má rozměry větší než 100x100px

2. **Reklama se nenačte**
   - Zkontrolujte VAST URL v konfiguraci
   - Ověřte dostupnost Google IMA SDK

3. **Instagram embed se nezobrazuje**
   - Zkontrolujte, zda není blokován `//www.instagram.com/embed.js`
   - Otevřete Developer Tools → Network tab

### Logování

Modul loguje události do konzole:

```
Instagram Video Ads: Spouštění...
Instagram Video Ads: Zpracovávám embed 0 (540x608px)
Instagram Video Ads: Overlay přidán
Instagram Video Ads: Spouštím reklamu...
Instagram Video Ads: Reklama dokončena
Instagram Video Ads: Vše vyčištěno
```
