/* Základn<PERSON> styly pro testova<PERSON><PERSON> stránku */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #fafafa;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    color: #262626;
    text-align: center;
    margin-bottom: 10px;
}

h2 {
    color: #262626;
    margin-top: 40px;
    margin-bottom: 15px;
}

p {
    color: #8e8e8e;
    text-align: center;
    margin-bottom: 30px;
}

.embed-container {
    margin: 30px 0;
    position: relative;
}

/* Styly pro overlay systém */
.instagram-ad-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.instagram-ad-overlay:hover {
    background: rgba(0, 0, 0, 0.8);
}

/* Play tlačítko s Instagram gradientem */
.instagram-play-button {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.instagram-play-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* Play ikona */
.instagram-play-button::before {
    content: '';
    width: 0;
    height: 0;
    border-left: 20px solid white;
    border-top: 12px solid transparent;
    border-bottom: 12px solid transparent;
    margin-left: 4px;
}

/* Kontejner pro video reklamu */
.instagram-ad-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    z-index: 999;
    border-radius: 3px;
    overflow: hidden;
}

.instagram-ad-video {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background: #000;
}

/* Loading indikátor */
.instagram-ad-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 16px;
    z-index: 1001;
}

/* Spinner animace */
.instagram-ad-spinner {
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 3px solid white;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error zpráva */
.instagram-ad-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    text-align: center;
    z-index: 1001;
    background: rgba(0, 0, 0, 0.8);
    padding: 20px;
    border-radius: 8px;
    max-width: 80%;
}

.instagram-ad-error h3 {
    margin: 0 0 10px 0;
    color: #ff6b6b;
}

.instagram-ad-error p {
    margin: 0;
    font-size: 14px;
    color: #ccc;
}

/* Responsivní design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }
    
    .instagram-play-button {
        width: 60px;
        height: 60px;
    }
    
    .instagram-play-button::before {
        border-left: 15px solid white;
        border-top: 9px solid transparent;
        border-bottom: 9px solid transparent;
        margin-left: 3px;
    }
}

/* Zajištění správného pozicování pro Instagram embedy */
.instagram-media {
    position: relative !important;
}

/* Skrytí původního obsahu během přehrávání reklamy */
.instagram-ad-playing .instagram-media > div {
    visibility: hidden;
}

.instagram-ad-playing .instagram-ad-container {
    visibility: visible;
}
