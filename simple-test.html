<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Video Ads - Jednoduchý test</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-status {
            background: #f0f8ff;
            border: 1px solid #0066cc;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .test-status h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .status-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-ok { background: #00ff00; }
        .status-error { background: #ff0000; }
        .status-warning { background: #ffaa00; }
        .manual-embed {
            background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
            color: white;
            padding: 40px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        .manual-embed h3 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .manual-embed p {
            margin: 5px 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Instagram Video Ads - Jednoduchý test</h1>
        
        <div class="test-status">
            <h3>Status testování:</h3>
            <div class="status-item">
                <div class="status-indicator" id="imaStatus"></div>
                <span>Google IMA SDK</span>
            </div>
            <div class="status-item">
                <div class="status-indicator" id="moduleStatus"></div>
                <span>Video Ads Module</span>
            </div>
            <div class="status-item">
                <div class="status-indicator" id="embedStatus"></div>
                <span>Instagram Embedy</span>
            </div>
        </div>

        <!-- Manuální embed pro test -->
        <div class="embed-container">
            <h2>Test embed 1</h2>
            <div class="manual-embed">
                <h3>📸 Instagram Post</h3>
                <p>Toto je testovací Instagram embed</p>
                <p>Klikněte na Play tlačítko pro spuštění reklamy</p>
            </div>
        </div>

        <!-- Druhý manuální embed -->
        <div class="embed-container">
            <h2>Test embed 2</h2>
            <div class="manual-embed">
                <h3>📱 Instagram Story</h3>
                <p>Další testovací embed</p>
                <p>Mělo by se objevit Play tlačítko</p>
            </div>
        </div>

        <!-- Debug informace -->
        <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 20px 0; font-family: monospace; font-size: 12px;">
            <h3>Debug informace:</h3>
            <div id="debugInfo">Načítání...</div>
        </div>
    </div>

    <!-- Skripty -->
    <script src="//imasdk.googleapis.com/js/sdkloader/ima3.js"></script>
    <script src="instagram-video-ads.js"></script>
    
    <script>
        // Status indikátory
        const imaStatus = document.getElementById('imaStatus');
        const moduleStatus = document.getElementById('moduleStatus');
        const embedStatus = document.getElementById('embedStatus');
        const debugInfo = document.getElementById('debugInfo');

        function updateStatus(element, status) {
            element.className = `status-indicator status-${status}`;
        }

        function updateDebugInfo() {
            let info = '';
            
            // IMA SDK status
            if (window.google && window.google.ima) {
                updateStatus(imaStatus, 'ok');
                info += 'Google IMA SDK: ✓ Dostupné\n';
            } else {
                updateStatus(imaStatus, 'error');
                info += 'Google IMA SDK: ✗ Nedostupné\n';
            }

            // Module status
            if (window.InstagramVideoAds) {
                updateStatus(moduleStatus, 'ok');
                const embeds = window.InstagramVideoAds.getEmbeds();
                info += `Video Ads Module: ✓ Načten (${embeds.length} embedů)\n`;
                
                embeds.forEach((embed, index) => {
                    info += `  Embed ${index}: ${embed.width}x${embed.height}px\n`;
                });
            } else {
                updateStatus(moduleStatus, 'error');
                info += 'Video Ads Module: ✗ Nenačten\n';
            }

            // Embeds status
            const embedElements = document.querySelectorAll('blockquote.instagram-media');
            if (embedElements.length > 0) {
                updateStatus(embedStatus, 'ok');
                info += `Instagram Embedy: ✓ ${embedElements.length} nalezeno\n`;
            } else {
                updateStatus(embedStatus, 'warning');
                info += 'Instagram Embedy: ⚠ Žádné nenalezeny (použije se fallback)\n';
            }

            debugInfo.textContent = info;
        }

        // Pravidelné aktualizace
        setInterval(updateDebugInfo, 2000);
        
        // První aktualizace po načtení
        setTimeout(updateDebugInfo, 1000);

        // Zachytávání console logů
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            if (args[0] && args[0].includes && args[0].includes('Instagram Video Ads:')) {
                const logDiv = document.createElement('div');
                logDiv.textContent = args[0];
                logDiv.style.cssText = 'color: #666; font-size: 11px; margin: 2px 0;';
                debugInfo.appendChild(logDiv);
            }
        };
    </script>
</body>
</html>
