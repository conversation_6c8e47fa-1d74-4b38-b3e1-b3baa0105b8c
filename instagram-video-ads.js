/**
 * Instagram Video Ads Module - Jednoduch<PERSON> verze
 *
 * Najde Instagram p<PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON> je layerem s Play tlačítkem
 * Po kliknutí přehraje video reklamu pomocí Google IMA SDK
 *
 * Použití:
 * 1. <PERSON><PERSON>žte Google IMA SDK: <script src="//imasdk.googleapis.com/js/sdkloader/ima3.js"></script>
 * 2. Vložte Instagram embed.js: <script async src="//www.instagram.com/embed.js"></script>
 * 3. Vložte tento skript: <script src="instagram-video-ads.js"></script>
 */

(function() {
    'use strict';

    // Konfigurace
    const CONFIG = {
        VAST_TAG_URL: 'https://pubads.g.doubleclick.net/gampad/ads?iu=/21775744923/external/single_ad_samples&sz=640x480&cust_params=sample_ct%3Dlinear&ciu_szs=300x250%2C728x90&gdfp_req=1&output=vast&unviewed_position_start=1&env=vp&impl=s&correlator='
    };

    let processedEmbeds = [];

    /**
     * Hlavní funkce - najde Instagram embedy a přidá overlay
     */
    function init() {
        console.log('Instagram Video Ads: Spouštění...');

        // Čekáme na Google IMA SDK
        if (!window.google || !window.google.ima) {
            console.log('Instagram Video Ads: Čekám na Google IMA SDK...');
            setTimeout(init, 1000);
            return;
        }

        // Hledáme Instagram embedy
        findAndProcessInstagramEmbeds();

        // Opakujeme každých 3 sekundy pro nově načtené embedy
        setInterval(findAndProcessInstagramEmbeds, 3000);
    }

    /**
     * Najde a zpracuje Instagram embedy
     */
    function findAndProcessInstagramEmbeds() {
        // Hledáme všechny Instagram embedy
        const embeds = document.querySelectorAll('blockquote.instagram-media');

        embeds.forEach((embed, index) => {
            // Přeskočíme už zpracované embedy
            if (processedEmbeds.includes(embed)) {
                return;
            }

            // Zkontrolujeme, zda má embed rozměry
            const rect = embed.getBoundingClientRect();
            if (rect.width > 100 && rect.height > 100) {
                console.log(`Instagram Video Ads: Zpracovávám embed ${index} (${Math.round(rect.width)}x${Math.round(rect.height)}px)`);
                addOverlayToEmbed(embed);
                processedEmbeds.push(embed);
            }
        });
    }

    /**
     * Přidá overlay s Play tlačítkem na Instagram embed
     */
    function addOverlayToEmbed(embed) {
        // Nastavíme position: relative pro embed
        embed.style.position = 'relative';

        // Vytvoříme overlay
        const overlay = document.createElement('div');
        overlay.className = 'instagram-ad-overlay';
        overlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            cursor: pointer;
            border-radius: 3px;
        `;

        // Vytvoříme Play tlačítko
        const playButton = document.createElement('div');
        playButton.style.cssText = `
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        `;

        // Play ikona
        playButton.innerHTML = `
            <div style="
                width: 0;
                height: 0;
                border-left: 20px solid white;
                border-top: 12px solid transparent;
                border-bottom: 12px solid transparent;
                margin-left: 4px;
            "></div>
        `;

        // Hover efekt
        playButton.addEventListener('mouseenter', () => {
            playButton.style.transform = 'scale(1.1)';
        });
        playButton.addEventListener('mouseleave', () => {
            playButton.style.transform = 'scale(1)';
        });

        overlay.appendChild(playButton);

        // Kliknutí na overlay
        overlay.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            playVideoAd(embed, overlay);
        });

        // Přidáme overlay do embedu
        embed.appendChild(overlay);

        console.log('Instagram Video Ads: Overlay přidán');
    }

    /**
     * Přehraje video reklamu
     */
    function playVideoAd(embed, overlay) {
        console.log('Instagram Video Ads: Spouštím reklamu...');

        // Získáme rozměry embedu
        const rect = embed.getBoundingClientRect();
        const width = Math.round(rect.width);
        const height = Math.round(rect.height);

        // Vytvoříme kontejner pro reklamu
        const adContainer = document.createElement('div');
        adContainer.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            z-index: 1001;
            border-radius: 3px;
        `;

        // Vytvoříme video element
        const videoElement = document.createElement('video');
        videoElement.style.cssText = `
            width: 100%;
            height: 100%;
            object-fit: contain;
        `;
        videoElement.setAttribute('playsinline', '');

        adContainer.appendChild(videoElement);
        embed.appendChild(adContainer);

        // Skryjeme overlay
        overlay.style.display = 'none';

        // Inicializujeme Google IMA
        try {
            const adDisplayContainer = new google.ima.AdDisplayContainer(adContainer, videoElement);
            const adsLoader = new google.ima.AdsLoader(adDisplayContainer);

            // Event listenery
            adsLoader.addEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED, (event) => {
                console.log('Instagram Video Ads: Reklama načtena');

                const adsManager = event.getAdsManager(videoElement);

                // Události reklamy
                adsManager.addEventListener(google.ima.AdEvent.Type.ALL_ADS_COMPLETED, () => {
                    console.log('Instagram Video Ads: Reklama dokončena');
                    cleanupAd(embed, overlay, adContainer, adsManager);
                });

                adsManager.addEventListener(google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED, () => {
                    console.log('Instagram Video Ads: Požadavek na obnovení obsahu');
                    cleanupAd(embed, overlay, adContainer, adsManager);
                });

                adsManager.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR, (errorEvent) => {
                    console.error('Instagram Video Ads: Chyba reklamy:', errorEvent.getError());
                    cleanupAd(embed, overlay, adContainer, adsManager);
                });

                // Spustíme reklamu
                adDisplayContainer.initialize();
                adsManager.init(width, height, google.ima.ViewMode.NORMAL);
                adsManager.start();
            });

            adsLoader.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR, (errorEvent) => {
                console.error('Instagram Video Ads: Chyba při načítání reklamy:', errorEvent.getError());
                cleanupAd(embed, overlay, adContainer, null);
            });

            // Vytvoříme požadavek na reklamu
            const adsRequest = new google.ima.AdsRequest();
            adsRequest.adTagUrl = CONFIG.VAST_TAG_URL + Date.now();
            adsRequest.linearAdSlotWidth = width;
            adsRequest.linearAdSlotHeight = height;

            // Požádáme o reklamu
            adsLoader.requestAds(adsRequest);

        } catch (error) {
            console.error('Instagram Video Ads: Chyba při inicializaci IMA:', error);
            cleanupAd(embed, overlay, adContainer, null);
        }
    }

    /**
     * Zpracování jednotlivého Instagram embedu
     */
    function processInstagramEmbed(embedElement, index) {
        // Okamžité zpracování bez čekání na rozměry
        const embedData = {
            element: embedElement,
            index: index,
            width: embedElement.offsetWidth || 540,
            height: embedElement.offsetHeight || 600,
            overlay: null,
            adContainer: null,
            adsManager: null,
            adDisplayContainer: null
        };

        instagramEmbeds.push(embedData);
        createOverlay(embedData);

        console.log(`Instagram Video Ads: Embed ${index} zpracován (${embedData.width}x${embedData.height})`);
    }

    // Funkce waitForEmbedDimensions odstraněna - nepotřebujeme čekat na rozměry

    /**
     * Vytvoření overlay s Play tlačítkem
     */
    function createOverlay(embedData) {
        const overlay = document.createElement('div');
        overlay.className = 'instagram-ad-overlay';

        const playButton = document.createElement('div');
        playButton.className = 'instagram-play-button';

        overlay.appendChild(playButton);

        // Event listener pro kliknutí
        overlay.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            playVideoAd(embedData);
        });

        // Vložení overlay do embedu
        embedData.element.style.position = 'relative';
        embedData.element.appendChild(overlay);
        embedData.overlay = overlay;
    }

    /**
     * Přehrání videoreklamy
     */
    function playVideoAd(embedData) {
        if (!isImaReady) {
            console.error('Instagram Video Ads: Google IMA SDK není připraveno');
            showError(embedData, 'SDK není připraveno');
            return;
        }

        console.log(`Instagram Video Ads: Spouštění reklamy pro embed ${embedData.index}`);

        // Skrytí overlay
        embedData.overlay.style.display = 'none';

        // Vytvoření kontejneru pro reklamu
        createAdContainer(embedData);

        // Zobrazení loading indikátoru
        showLoading(embedData);

        // Inicializace IMA
        initializeIMA(embedData);
    }

    /**
     * Vytvoření kontejneru pro video reklamu
     */
    function createAdContainer(embedData) {
        const adContainer = document.createElement('div');
        adContainer.className = 'instagram-ad-container';

        const videoElement = document.createElement('video');
        videoElement.className = 'instagram-ad-video';
        videoElement.setAttribute('playsinline', '');
        videoElement.setAttribute('webkit-playsinline', '');

        adContainer.appendChild(videoElement);
        embedData.element.appendChild(adContainer);
        embedData.adContainer = adContainer;
        embedData.videoElement = videoElement;
    }

    /**
     * Zobrazení loading indikátoru
     */
    function showLoading(embedData) {
        const loading = document.createElement('div');
        loading.className = 'instagram-ad-loading';
        loading.innerHTML = `
            <div class="instagram-ad-spinner"></div>
            <div>Načítání reklamy...</div>
        `;
        embedData.adContainer.appendChild(loading);
        embedData.loadingElement = loading;
    }

    /**
     * Skrytí loading indikátoru
     */
    function hideLoading(embedData) {
        if (embedData.loadingElement) {
            embedData.loadingElement.remove();
            embedData.loadingElement = null;
        }
    }

    /**
     * Zobrazení chybové zprávy
     */
    function showError(embedData, message) {
        hideLoading(embedData);

        const error = document.createElement('div');
        error.className = 'instagram-ad-error';
        error.innerHTML = `
            <h3>Chyba při načítání reklamy</h3>
            <p>${message}</p>
            <p>Klikněte kamkoliv pro návrat k obsahu.</p>
        `;

        error.addEventListener('click', () => {
            cleanupAd(embedData);
        });

        if (embedData.adContainer) {
            embedData.adContainer.appendChild(error);
        }

        // Automatické ukončení po 5 sekundách
        setTimeout(() => {
            cleanupAd(embedData);
        }, 5000);
    }

    /**
     * Inicializace Google IMA SDK
     */
    function initializeIMA(embedData) {
        try {
            // Vytvoření AdDisplayContainer
            embedData.adDisplayContainer = new google.ima.AdDisplayContainer(
                embedData.adContainer,
                embedData.videoElement
            );

            // Vytvoření AdsLoader
            embedData.adsLoader = new google.ima.AdsLoader(embedData.adDisplayContainer);

            // Event listenery pro AdsLoader
            embedData.adsLoader.addEventListener(
                google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,
                (event) => onAdsManagerLoaded(embedData, event),
                false
            );

            embedData.adsLoader.addEventListener(
                google.ima.AdErrorEvent.Type.AD_ERROR,
                (event) => onAdError(embedData, event),
                false
            );

            // Vytvoření AdsRequest
            const adsRequest = new google.ima.AdsRequest();
            adsRequest.adTagUrl = CONFIG.VAST_TAG_URL + Date.now(); // Přidání correlatoru
            adsRequest.linearAdSlotWidth = embedData.width;
            adsRequest.linearAdSlotHeight = embedData.height;

            // Požádání o reklamy
            embedData.adsLoader.requestAds(adsRequest);

        } catch (error) {
            console.error('Instagram Video Ads: Chyba při inicializaci IMA:', error);
            showError(embedData, 'Chyba při inicializaci přehrávače');
        }
    }

    /**
     * Handler pro načtení AdsManageru
     */
    function onAdsManagerLoaded(embedData, adsManagerLoadedEvent) {
        console.log('Instagram Video Ads: AdsManager načten');

        hideLoading(embedData);

        // Získání AdsManageru
        const adsRenderingSettings = new google.ima.AdsRenderingSettings();
        adsRenderingSettings.restoreCustomPlaybackStateOnAdBreakComplete = true;

        embedData.adsManager = adsManagerLoadedEvent.getAdsManager(
            embedData.videoElement,
            adsRenderingSettings
        );

        // Event listenery pro AdsManager
        embedData.adsManager.addEventListener(
            google.ima.AdErrorEvent.Type.AD_ERROR,
            (event) => onAdError(embedData, event)
        );

        embedData.adsManager.addEventListener(
            google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED,
            () => console.log('Instagram Video Ads: Content pause requested')
        );

        embedData.adsManager.addEventListener(
            google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED,
            () => onContentResumeRequested(embedData)
        );

        embedData.adsManager.addEventListener(
            google.ima.AdEvent.Type.ALL_ADS_COMPLETED,
            () => onAllAdsCompleted(embedData)
        );

        embedData.adsManager.addEventListener(
            google.ima.AdEvent.Type.LOADED,
            () => console.log('Instagram Video Ads: Reklama načtena')
        );

        embedData.adsManager.addEventListener(
            google.ima.AdEvent.Type.STARTED,
            () => console.log('Instagram Video Ads: Reklama spuštěna')
        );

        try {
            // Inicializace AdDisplayContainer
            embedData.adDisplayContainer.initialize();

            // Inicializace a spuštění AdsManageru
            embedData.adsManager.init(embedData.width, embedData.height, google.ima.ViewMode.NORMAL);
            embedData.adsManager.start();

        } catch (error) {
            console.error('Instagram Video Ads: Chyba při spuštění AdsManageru:', error);
            showError(embedData, 'Chyba při spuštění reklamy');
        }
    }

    /**
     * Handler pro chyby reklamy
     */
    function onAdError(embedData, adErrorEvent) {
        console.error('Instagram Video Ads: Chyba reklamy:', adErrorEvent.getError());
        showError(embedData, 'Reklama není dostupná');
    }

    /**
     * Handler pro dokončení všech reklam
     */
    function onAllAdsCompleted(embedData) {
        console.log('Instagram Video Ads: Všechny reklamy dokončeny');
        cleanupAd(embedData);
    }

    /**
     * Handler pro požadavek na obnovení obsahu
     */
    function onContentResumeRequested(embedData) {
        console.log('Instagram Video Ads: Požadavek na obnovení obsahu');
        cleanupAd(embedData);
    }

    /**
     * Ukončení a vyčištění reklamy
     */
    function cleanupAd(embedData) {
        console.log(`Instagram Video Ads: Ukončování reklamy pro embed ${embedData.index}`);

        // Zničení AdsManageru
        if (embedData.adsManager) {
            embedData.adsManager.destroy();
            embedData.adsManager = null;
        }

        // Odstranění ad kontejneru
        if (embedData.adContainer) {
            embedData.adContainer.remove();
            embedData.adContainer = null;
        }

        // ÚPLNÉ ODSTRANĚNÍ overlay - nezobrazujeme ho znovu
        if (embedData.overlay) {
            embedData.overlay.remove();
            embedData.overlay = null;
        }

        // Vyčištění všech elementů
        embedData.loadingElement = null;
        embedData.videoElement = null;
        embedData.adDisplayContainer = null;
        embedData.adsLoader = null;

        console.log(`Instagram Video Ads: Embed ${embedData.index} kompletně vyčištěn - overlay odstraněn`);
    }

    // Inicializace po načtení stránky
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            // Krátké zpoždění pro načtení IMA SDK
            setTimeout(init, 500);
        });
    } else {
        // DOM je již načten, spustíme okamžitě
        setTimeout(init, 100);
    }

    // Export pro debugging (volitelné)
    window.InstagramVideoAds = {
        getEmbeds: () => instagramEmbeds,
        getConfig: () => CONFIG,
        reinitialize: init
    };

})();
