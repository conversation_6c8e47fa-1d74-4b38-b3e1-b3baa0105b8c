<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Video Ads - Debug</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .debug-console {
            background: #1e1e1e;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            height: 300px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        .debug-controls {
            margin: 10px 0;
        }
        .debug-controls button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .debug-controls button:hover {
            background: #005a87;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background: #00ff00; }
        .status-warning { background: #ffaa00; }
        .status-error { background: #ff0000; }
        .status-info { background: #0088ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Instagram Video Ads - Debug Console</h1>
        
        <div class="debug-controls">
            <button onclick="startMonitoring()">Spustit monitoring</button>
            <button onclick="stopMonitoring()">Zastavit monitoring</button>
            <button onclick="clearConsole()">Vymazat konzoli</button>
            <button onclick="testInstagramEmbeds()">Test Instagram embedů</button>
            <button onclick="testIMASDK()">Test IMA SDK</button>
        </div>

        <div class="debug-console" id="debugConsole">
            <div>Instagram Video Ads Debug Console</div>
            <div>=====================================</div>
        </div>

        <!-- Test Instagram embed -->
        <div class="embed-container">
            <h2>Test Instagram embed</h2>
            <blockquote class="instagram-media" data-instgrm-captioned data-instgrm-permalink="https://www.instagram.com/p/DKMANWSMaeq/?utm_source=ig_embed&utm_campaign=loading" data-instgrm-version="14" style="background:#FFF; border:0; border-radius:3px; box-shadow:0 0 1px 0 rgba(0,0,0,0.5),0 1px 10px 0 rgba(0,0,0,0.15); margin: 1px; max-width:540px; min-width:326px; padding:0; width:99.375%; width:-webkit-calc(100% - 2px); width:calc(100% - 2px);">
                <div style="padding:16px;">
                    <a href="https://www.instagram.com/p/DKMANWSMaeq/?utm_source=ig_embed&utm_campaign=loading" style="background:#FFFFFF; line-height:0; padding:0 0; text-align:center; text-decoration:none; width:100%;" target="_blank">
                        <div style="display: flex; flex-direction: row; align-items: center;">
                            <div style="background-color: #F4F4F4; border-radius: 50%; flex-grow: 0; height: 40px; margin-right: 14px; width: 40px;"></div>
                            <div style="display: flex; flex-direction: column; flex-grow: 1; justify-content: center;">
                                <div style="background-color: #F4F4F4; border-radius: 4px; flex-grow: 0; height: 14px; margin-bottom: 6px; width: 100px;"></div>
                                <div style="background-color: #F4F4F4; border-radius: 4px; flex-grow: 0; height: 14px; width: 60px;"></div>
                            </div>
                        </div>
                        <div style="padding: 19% 0;"></div>
                        <div style="display:block; height:50px; margin:0 auto 12px; width:50px;">
                            <svg width="50px" height="50px" viewBox="0 0 60 60" version="1.1" xmlns="https://www.w3.org/2000/svg">
                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g transform="translate(-511.000000, -20.000000)" fill="#000000">
                                        <path d="M556.869,30.41 C554.814,30.41 553.148,32.076 553.148,34.131 C553.148,36.186 554.814,37.852 556.869,37.852 C558.924,37.852 560.59,36.186 560.59,34.131 C560.59,32.076 558.924,30.41 556.869,30.41 M541,60.657 C535.114,60.657 530.342,55.887 530.342,50 C530.342,44.114 535.114,39.342 541,39.342 C546.887,39.342 551.658,44.114 551.658,50 C551.658,55.887 546.887,60.657 541,60.657 M541,33.886 C532.1,33.886 524.886,41.1 524.886,50 C524.886,58.899 532.1,66.113 541,66.113 C549.9,66.113 557.115,58.899 557.115,50 C557.115,41.1 549.9,33.886 541,33.886"></path>
                                    </g>
                                </g>
                            </svg>
                        </div>
                        <div style="padding-top: 8px;">
                            <div style="color:#3897f0; font-family:Arial,sans-serif; font-size:14px; font-style:normal; font-weight:550; line-height:18px;">
                                Zobrazit příspěvek na Instagramu
                            </div>
                        </div>
                        <div style="padding: 12.5% 0;"></div>
                    </a>
                    <p style="color:#c9c8cd; font-family:Arial,sans-serif; font-size:14px; line-height:17px; margin-bottom:0; margin-top:8px; overflow:hidden; padding:8px 0 7px; text-align:center; text-overflow:ellipsis; white-space:nowrap;">
                        <a href="https://www.instagram.com/p/DKMANWSMaeq/?utm_source=ig_embed&utm_campaign=loading" style="color:#c9c8cd; font-family:Arial,sans-serif; font-size:14px; font-style:normal; font-weight:normal; line-height:17px; text-decoration:none;" target="_blank">Příspěvek sdílený Michaela Kacena (@michaela.kacena)</a>
                    </p>
                </div>
            </blockquote>
        </div>
    </div>

    <!-- Skripty -->
    <script async src="//www.instagram.com/embed.js"></script>
    <script src="//imasdk.googleapis.com/js/sdkloader/ima3.js"></script>
    <script src="instagram-video-ads.js"></script>
    
    <script>
        let monitoringInterval = null;
        const debugConsole = document.getElementById('debugConsole');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = `status-${type}`;
            const line = document.createElement('div');
            line.innerHTML = `<span class="status-indicator ${statusClass}"></span>[${timestamp}] ${message}`;
            debugConsole.appendChild(line);
            debugConsole.scrollTop = debugConsole.scrollHeight;
        }

        function clearConsole() {
            debugConsole.innerHTML = `
                <div>Instagram Video Ads Debug Console</div>
                <div>=====================================</div>
            `;
        }

        function startMonitoring() {
            if (monitoringInterval) {
                stopMonitoring();
            }
            
            log('Spouštím monitoring...', 'info');
            
            monitoringInterval = setInterval(() => {
                checkInstagramEmbeds();
                checkIMASDK();
                checkVideoAdsModule();
            }, 2000);
        }

        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                log('Monitoring zastaven', 'warning');
            }
        }

        function checkInstagramEmbeds() {
            const embeds = document.querySelectorAll('blockquote.instagram-media');
            
            if (embeds.length === 0) {
                log('Žádné Instagram embedy nenalezeny', 'warning');
                return;
            }

            embeds.forEach((embed, index) => {
                const width = embed.offsetWidth;
                const height = embed.offsetHeight;
                const iframe = embed.querySelector('iframe');
                
                if (width > 0 && height > 0) {
                    log(`Embed ${index}: ${width}x${height}px ${iframe ? '(s iframe)' : '(bez iframe)'}`, 'ok');
                } else {
                    log(`Embed ${index}: Neplatné rozměry (${width}x${height})`, 'error');
                }
            });
        }

        function checkIMASDK() {
            if (window.google && window.google.ima) {
                log('Google IMA SDK: Dostupné ✓', 'ok');
            } else {
                log('Google IMA SDK: Nedostupné ✗', 'error');
            }
        }

        function checkVideoAdsModule() {
            if (window.InstagramVideoAds) {
                const embeds = window.InstagramVideoAds.getEmbeds();
                const config = window.InstagramVideoAds.getConfig();
                
                log(`Video Ads Module: ${embeds.length} embedů zpracováno`, 'ok');
                
                embeds.forEach((embed, index) => {
                    const hasOverlay = embed.overlay ? 'má overlay' : 'bez overlay';
                    log(`  Embed ${index}: ${embed.width}x${embed.height}px, ${hasOverlay}`, 'info');
                });
            } else {
                log('Video Ads Module: Nedostupný', 'error');
            }
        }

        function testInstagramEmbeds() {
            log('=== TEST INSTAGRAM EMBEDŮ ===', 'info');
            checkInstagramEmbeds();
        }

        function testIMASDK() {
            log('=== TEST IMA SDK ===', 'info');
            checkIMASDK();
            
            if (window.google && window.google.ima) {
                try {
                    const testContainer = document.createElement('div');
                    const testVideo = document.createElement('video');
                    const adDisplayContainer = new google.ima.AdDisplayContainer(testContainer, testVideo);
                    log('IMA AdDisplayContainer: Vytvořen úspěšně ✓', 'ok');
                } catch (error) {
                    log(`IMA AdDisplayContainer: Chyba - ${error.message}`, 'error');
                }
            }
        }

        // Automatické spuštění monitoringu
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Stránka načtena, spouštím monitoring...', 'info');
                startMonitoring();
            }, 1000);
        });

        // Zachytávání console.log z našeho modulu
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            originalLog.apply(console, args);
            if (args[0] && args[0].includes && args[0].includes('Instagram Video Ads:')) {
                log(args[0], 'info');
            }
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            if (args[0] && args[0].includes && args[0].includes('Instagram Video Ads:')) {
                log(args[0], 'error');
            }
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            if (args[0] && args[0].includes && args[0].includes('Instagram Video Ads:')) {
                log(args[0], 'warning');
            }
        };
    </script>
</body>
</html>
