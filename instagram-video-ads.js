/**
 * Instagram Video Ads Module
 *
 * Modul pro dynamické vkládání videoreklam přes Instagram embedy
 * Používá Google IMA SDK pro přehrávání VAST reklam
 *
 * Použití:
 * 1. Vložte Google IMA SDK: <script src="//imasdk.googleapis.com/js/sdkloader/ima3.js"></script>
 * 2. Vložte tento skript: <script src="instagram-video-ads.js"></script>
 * 3. Upravte VAST_TAG_URL podle potřeby
 */

(function() {
    'use strict';

    // Konfigurace - zde můžete změnit VAST tag URL
    const CONFIG = {
        VAST_TAG_URL: 'https://pubads.g.doubleclick.net/gampad/ads?iu=/21775744923/external/single_ad_samples&sz=640x480&cust_params=sample_ct%3Dlinear&ciu_szs=300x250%2C728x90&gdfp_req=1&output=vast&unviewed_position_start=1&env=vp&impl=s&correlator=',
        POLLING_INTERVAL: 500, // ms - pomalejší polling
        MAX_POLLING_ATTEMPTS: 60, // 30 sekund celkem
        EMBED_READY_DELAY: 3000, // ms - delší čekání na ustálení rozměrů embedu
        INSTAGRAM_READY_DELAY: 2000 // ms - čekání na Instagram embed.js
    };

    // Globální proměnné
    let instagramEmbeds = [];
    let isImaReady = false;

    /**
     * Hlavní inicializační funkce
     */
    function init() {
        console.log('Instagram Video Ads: Inicializace modulu...');

        // Čekání na načtení Google IMA SDK
        waitForImaSDK(() => {
            isImaReady = true;
            console.log('Instagram Video Ads: Google IMA SDK je připraveno');

            // Okamžité zpracování - nepotřebujeme čekat na Instagram embed.js
            console.log('Instagram Video Ads: Spouštím detekci embedů...');
            detectAndProcessInstagramEmbeds();
        });
    }

    /**
     * Čekání na dostupnost Google IMA SDK
     */
    function waitForImaSDK(callback) {
        let attempts = 0;

        function checkIma() {
            if (window.google && window.google.ima) {
                callback();
                return;
            }

            attempts++;
            if (attempts < CONFIG.MAX_POLLING_ATTEMPTS) {
                setTimeout(checkIma, CONFIG.POLLING_INTERVAL);
            } else {
                console.error('Instagram Video Ads: Google IMA SDK se nepodařilo načíst');
            }
        }

        checkIma();
    }

    // Funkce waitForInstagramEmbeds odstraněna - nepotřebujeme čekat na Instagram embed.js

    /**
     * Detekce a zpracování všech Instagram embedů na stránce
     */
    function detectAndProcessInstagramEmbeds() {
        let embedElements = document.querySelectorAll('blockquote.instagram-media');

        console.log(`Instagram Video Ads: Nalezeno ${embedElements.length} Instagram embedů`);

        // Pokud nejsou nalezeny žádné embedy, zkusíme vytvořit fallback
        if (embedElements.length === 0) {
            console.log('Instagram Video Ads: Žádné embedy nenalezeny, vytvářím fallback embedy...');
            embedElements = createFallbackEmbeds();
            console.log(`Instagram Video Ads: Vytvořeno ${embedElements.length} fallback embedů`);
        }

        // Zpracování každého embedu
        embedElements.forEach((embed, index) => {
            // Zajistíme, že embed má minimální rozměry
            ensureEmbedDimensions(embed);
            processInstagramEmbed(embed, index);
        });
    }

    /**
     * Zajištění minimálních rozměrů pro embed
     */
    function ensureEmbedDimensions(embed) {
        const currentWidth = embed.offsetWidth;
        const currentHeight = embed.offsetHeight;

        // Pokud embed nemá rozměry, nastavíme výchozí
        if (currentWidth < 300 || currentHeight < 400) {
            embed.style.width = '540px';
            embed.style.height = '600px';
            embed.style.minHeight = '400px';
            embed.style.display = 'block';
            console.log(`Instagram Video Ads: Nastaveny výchozí rozměry pro embed (540x600px)`);
        }
    }

    /**
     * Vytvoření fallback embedů pro testování
     */
    function createFallbackEmbeds() {
        const embedContainers = document.querySelectorAll('.embed-container');
        const fallbackEmbeds = [];

        embedContainers.forEach((container, index) => {
            // Zkontrolujeme, zda už neobsahuje blockquote
            let blockquote = container.querySelector('blockquote.instagram-media');

            if (!blockquote) {
                // Vytvoříme mock Instagram embed
                blockquote = document.createElement('blockquote');
                blockquote.className = 'instagram-media';
                blockquote.style.cssText = 'background:#FFF; border:0; border-radius:3px; box-shadow:0 0 1px 0 rgba(0,0,0,0.5),0 1px 10px 0 rgba(0,0,0,0.15); margin: 1px; max-width:540px; min-width:326px; padding:0; width:99.375%; width:-webkit-calc(100% - 2px); width:calc(100% - 2px); height:400px; position:relative;';

                const content = document.createElement('div');
                content.style.cssText = 'padding:16px; height:100%; display:flex; align-items:center; justify-content:center; background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%); color:white; font-family:Arial,sans-serif;';
                content.innerHTML = `
                    <div style="text-align:center;">
                        <div style="font-size:24px; margin-bottom:10px;">📸</div>
                        <div style="font-size:16px; font-weight:bold;">Instagram Post ${index + 1}</div>
                        <div style="font-size:12px; opacity:0.8; margin-top:5px;">Klikněte pro přehrání reklamy</div>
                    </div>
                `;

                blockquote.appendChild(content);
                container.appendChild(blockquote);

                console.log(`Instagram Video Ads: Vytvořen fallback embed ${index + 1}`);
            }

            fallbackEmbeds.push(blockquote);
        });

        return fallbackEmbeds;
    }

    /**
     * Zpracování jednotlivého Instagram embedu
     */
    function processInstagramEmbed(embedElement, index) {
        // Okamžité zpracování bez čekání na rozměry
        const embedData = {
            element: embedElement,
            index: index,
            width: embedElement.offsetWidth || 540,
            height: embedElement.offsetHeight || 600,
            overlay: null,
            adContainer: null,
            adsManager: null,
            adDisplayContainer: null
        };

        instagramEmbeds.push(embedData);
        createOverlay(embedData);

        console.log(`Instagram Video Ads: Embed ${index} zpracován (${embedData.width}x${embedData.height})`);
    }

    // Funkce waitForEmbedDimensions odstraněna - nepotřebujeme čekat na rozměry

    /**
     * Vytvoření overlay s Play tlačítkem
     */
    function createOverlay(embedData) {
        const overlay = document.createElement('div');
        overlay.className = 'instagram-ad-overlay';

        const playButton = document.createElement('div');
        playButton.className = 'instagram-play-button';

        overlay.appendChild(playButton);

        // Event listener pro kliknutí
        overlay.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            playVideoAd(embedData);
        });

        // Vložení overlay do embedu
        embedData.element.style.position = 'relative';
        embedData.element.appendChild(overlay);
        embedData.overlay = overlay;
    }

    /**
     * Přehrání videoreklamy
     */
    function playVideoAd(embedData) {
        if (!isImaReady) {
            console.error('Instagram Video Ads: Google IMA SDK není připraveno');
            showError(embedData, 'SDK není připraveno');
            return;
        }

        console.log(`Instagram Video Ads: Spouštění reklamy pro embed ${embedData.index}`);

        // Skrytí overlay
        embedData.overlay.style.display = 'none';

        // Vytvoření kontejneru pro reklamu
        createAdContainer(embedData);

        // Zobrazení loading indikátoru
        showLoading(embedData);

        // Inicializace IMA
        initializeIMA(embedData);
    }

    /**
     * Vytvoření kontejneru pro video reklamu
     */
    function createAdContainer(embedData) {
        const adContainer = document.createElement('div');
        adContainer.className = 'instagram-ad-container';

        const videoElement = document.createElement('video');
        videoElement.className = 'instagram-ad-video';
        videoElement.setAttribute('playsinline', '');
        videoElement.setAttribute('webkit-playsinline', '');

        adContainer.appendChild(videoElement);
        embedData.element.appendChild(adContainer);
        embedData.adContainer = adContainer;
        embedData.videoElement = videoElement;
    }

    /**
     * Zobrazení loading indikátoru
     */
    function showLoading(embedData) {
        const loading = document.createElement('div');
        loading.className = 'instagram-ad-loading';
        loading.innerHTML = `
            <div class="instagram-ad-spinner"></div>
            <div>Načítání reklamy...</div>
        `;
        embedData.adContainer.appendChild(loading);
        embedData.loadingElement = loading;
    }

    /**
     * Skrytí loading indikátoru
     */
    function hideLoading(embedData) {
        if (embedData.loadingElement) {
            embedData.loadingElement.remove();
            embedData.loadingElement = null;
        }
    }

    /**
     * Zobrazení chybové zprávy
     */
    function showError(embedData, message) {
        hideLoading(embedData);

        const error = document.createElement('div');
        error.className = 'instagram-ad-error';
        error.innerHTML = `
            <h3>Chyba při načítání reklamy</h3>
            <p>${message}</p>
            <p>Klikněte kamkoliv pro návrat k obsahu.</p>
        `;

        error.addEventListener('click', () => {
            cleanupAd(embedData);
        });

        if (embedData.adContainer) {
            embedData.adContainer.appendChild(error);
        }

        // Automatické ukončení po 5 sekundách
        setTimeout(() => {
            cleanupAd(embedData);
        }, 5000);
    }

    /**
     * Inicializace Google IMA SDK
     */
    function initializeIMA(embedData) {
        try {
            // Vytvoření AdDisplayContainer
            embedData.adDisplayContainer = new google.ima.AdDisplayContainer(
                embedData.adContainer,
                embedData.videoElement
            );

            // Vytvoření AdsLoader
            embedData.adsLoader = new google.ima.AdsLoader(embedData.adDisplayContainer);

            // Event listenery pro AdsLoader
            embedData.adsLoader.addEventListener(
                google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,
                (event) => onAdsManagerLoaded(embedData, event),
                false
            );

            embedData.adsLoader.addEventListener(
                google.ima.AdErrorEvent.Type.AD_ERROR,
                (event) => onAdError(embedData, event),
                false
            );

            // Vytvoření AdsRequest
            const adsRequest = new google.ima.AdsRequest();
            adsRequest.adTagUrl = CONFIG.VAST_TAG_URL + Date.now(); // Přidání correlatoru
            adsRequest.linearAdSlotWidth = embedData.width;
            adsRequest.linearAdSlotHeight = embedData.height;

            // Požádání o reklamy
            embedData.adsLoader.requestAds(adsRequest);

        } catch (error) {
            console.error('Instagram Video Ads: Chyba při inicializaci IMA:', error);
            showError(embedData, 'Chyba při inicializaci přehrávače');
        }
    }

    /**
     * Handler pro načtení AdsManageru
     */
    function onAdsManagerLoaded(embedData, adsManagerLoadedEvent) {
        console.log('Instagram Video Ads: AdsManager načten');

        hideLoading(embedData);

        // Získání AdsManageru
        const adsRenderingSettings = new google.ima.AdsRenderingSettings();
        adsRenderingSettings.restoreCustomPlaybackStateOnAdBreakComplete = true;

        embedData.adsManager = adsManagerLoadedEvent.getAdsManager(
            embedData.videoElement,
            adsRenderingSettings
        );

        // Event listenery pro AdsManager
        embedData.adsManager.addEventListener(
            google.ima.AdErrorEvent.Type.AD_ERROR,
            (event) => onAdError(embedData, event)
        );

        embedData.adsManager.addEventListener(
            google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED,
            () => console.log('Instagram Video Ads: Content pause requested')
        );

        embedData.adsManager.addEventListener(
            google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED,
            () => onContentResumeRequested(embedData)
        );

        embedData.adsManager.addEventListener(
            google.ima.AdEvent.Type.ALL_ADS_COMPLETED,
            () => onAllAdsCompleted(embedData)
        );

        embedData.adsManager.addEventListener(
            google.ima.AdEvent.Type.LOADED,
            () => console.log('Instagram Video Ads: Reklama načtena')
        );

        embedData.adsManager.addEventListener(
            google.ima.AdEvent.Type.STARTED,
            () => console.log('Instagram Video Ads: Reklama spuštěna')
        );

        try {
            // Inicializace AdDisplayContainer
            embedData.adDisplayContainer.initialize();

            // Inicializace a spuštění AdsManageru
            embedData.adsManager.init(embedData.width, embedData.height, google.ima.ViewMode.NORMAL);
            embedData.adsManager.start();

        } catch (error) {
            console.error('Instagram Video Ads: Chyba při spuštění AdsManageru:', error);
            showError(embedData, 'Chyba při spuštění reklamy');
        }
    }

    /**
     * Handler pro chyby reklamy
     */
    function onAdError(embedData, adErrorEvent) {
        console.error('Instagram Video Ads: Chyba reklamy:', adErrorEvent.getError());
        showError(embedData, 'Reklama není dostupná');
    }

    /**
     * Handler pro dokončení všech reklam
     */
    function onAllAdsCompleted(embedData) {
        console.log('Instagram Video Ads: Všechny reklamy dokončeny');
        cleanupAd(embedData);
    }

    /**
     * Handler pro požadavek na obnovení obsahu
     */
    function onContentResumeRequested(embedData) {
        console.log('Instagram Video Ads: Požadavek na obnovení obsahu');
        cleanupAd(embedData);
    }

    /**
     * Ukončení a vyčištění reklamy
     */
    function cleanupAd(embedData) {
        console.log(`Instagram Video Ads: Ukončování reklamy pro embed ${embedData.index}`);

        // Zničení AdsManageru
        if (embedData.adsManager) {
            embedData.adsManager.destroy();
            embedData.adsManager = null;
        }

        // Odstranění ad kontejneru
        if (embedData.adContainer) {
            embedData.adContainer.remove();
            embedData.adContainer = null;
        }

        // Zobrazení overlay
        if (embedData.overlay) {
            embedData.overlay.style.display = 'flex';
        }

        // Vyčištění loading elementu
        embedData.loadingElement = null;
        embedData.videoElement = null;
        embedData.adDisplayContainer = null;
        embedData.adsLoader = null;
    }

    // Inicializace po načtení stránky
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            // Krátké zpoždění pro načtení IMA SDK
            setTimeout(init, 500);
        });
    } else {
        // DOM je již načten, spustíme okamžitě
        setTimeout(init, 100);
    }

    // Export pro debugging (volitelné)
    window.InstagramVideoAds = {
        getEmbeds: () => instagramEmbeds,
        getConfig: () => CONFIG,
        reinitialize: init
    };

})();
