# Instagram Video Ads Module

Samostatný JavaScriptový modul pro dynamické vkládání videoreklam přes existující Instagram embedy na webových stránkách.

## Funkcionality

- ✅ <PERSON>k<PERSON> detekce Instagram embedů na stránce
- ✅ Vytvoření interaktivní překryvné vrstvy s Play tlačítkem
- ✅ Integrace s Google IMA SDK pro přehrávání VAST reklam
- ✅ Responsivní design s Instagram barevným schématem
- ✅ Správa chyb a loading stavů
- ✅ Podpora více embedů na jedné stránce
- ✅ Automatické obnovení původního obsahu po reklamě

## Instalace a použití

### 1. Základní implementace

```html
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- <PERSON><PERSON><PERSON> Instagram embed -->
    <blockquote class="instagram-media" data-instgrm-permalink="...">
        <!-- Instagram obsah -->
    </blockquote>

    <!-- <PERSON><PERSON><PERSON><PERSON> skripty -->
    <script async src="//www.instagram.com/embed.js"></script>
    <script src="//imasdk.googleapis.com/js/sdkloader/ima3.js"></script>
    <script src="instagram-video-ads.js"></script>
</body>
</html>
```

### 2. Konfigurace VAST URL

Pro změnu VAST tag URL upravte konstantu `VAST_TAG_URL` v souboru `instagram-video-ads.js`:

```javascript
const CONFIG = {
    VAST_TAG_URL: 'https://your-vast-server.com/vast-tag-url',
    // ... další konfigurace
};
```

### 3. Testování

Otevřete `index.html` v prohlížeči pro testování funkcionality s ukázkovými Instagram embedy.

Pro debugging použijte:
- `test.html` - Jednoduchý test s debug panelem
- `debug.html` - Pokročilý monitoring a diagnostika

## Technické detaily

### Architektura

Modul funguje v následujících krocích:

1. **Detekce embedů**: Vyhledá všechny `blockquote.instagram-media` elementy
2. **Čekání na rozměry**: Počká na ustálení rozměrů po načtení Instagram embed.js
3. **Vytvoření overlay**: Přidá poloprůhlednou vrstvu s Play tlačítkem
4. **IMA inicializace**: Po kliknutí inicializuje Google IMA SDK
5. **Přehrání reklamy**: Načte a přehraje VAST reklamu
6. **Cleanup**: Po dokončení obnoví původní stav

### Klíčové komponenty

- **`detectAndProcessInstagramEmbeds()`**: Hlavní detekční logika
- **`createOverlay()`**: Vytvoření interaktivní vrstvy
- **`initializeIMA()`**: Integrace s Google IMA SDK
- **`cleanupAd()`**: Ukončení a vyčištění po reklamě

### Event handling

Modul zpracovává následující události:

- `ADS_MANAGER_LOADED`: Úspěšné načtení reklamy
- `AD_ERROR`: Chyby při načítání nebo přehrávání
- `ALL_ADS_COMPLETED`: Dokončení všech reklam
- `CONTENT_RESUME_REQUESTED`: Požadavek na obnovení obsahu

## Konfigurace

### Dostupné možnosti

```javascript
const CONFIG = {
    VAST_TAG_URL: 'https://...',           // URL pro VAST tag
    POLLING_INTERVAL: 100,                 // Interval pro polling (ms)
    MAX_POLLING_ATTEMPTS: 50,              // Max počet pokusů
    EMBED_READY_DELAY: 1000               // Zpoždění pro ustálení embedů (ms)
};
```

### Styling

Všechny styly jsou v `styles.css`. Klíčové CSS třídy:

- `.instagram-ad-overlay`: Překryvná vrstva
- `.instagram-play-button`: Play tlačítko s Instagram gradientem
- `.instagram-ad-container`: Kontejner pro video reklamu
- `.instagram-ad-loading`: Loading indikátor
- `.instagram-ad-error`: Chybové zprávy

## Kompatibilita

### Podporované prohlížeče

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

### Požadavky

- Google IMA SDK v3
- Instagram embed.js
- Moderní prohlížeč s podporou ES6+

## Debugging

Modul poskytuje debugging rozhraní přes `window.InstagramVideoAds`:

```javascript
// Získání seznamu zpracovaných embedů
console.log(window.InstagramVideoAds.getEmbeds());

// Zobrazení konfigurace
console.log(window.InstagramVideoAds.getConfig());

// Reinicializace modulu
window.InstagramVideoAds.reinitialize();
```

## Řešení problémů

### Časté problémy

1. **Overlay se nezobrazí**
   - **Příčina**: Instagram embedy se nenačítají správně
   - **Řešení**: Otevřete `debug.html` pro monitoring stavu embedů
   - **Kontrola**: Ověřte, že Instagram embed.js je načten a embedy mají správné rozměry

2. **Reklama se nenačte**
   - **Příčina**: Problém s Google IMA SDK nebo VAST URL
   - **Řešení**: Zkontrolujte konzoli prohlížeče a síťové požadavky
   - **Kontrola**: Ověřte dostupnost IMA SDK pomocí `debug.html`

3. **Instagram embedy se nezobrazují**
   - **Příčina**: Blokování externích skriptů nebo CORS problémy
   - **Řešení**: Zkontrolujte, zda není blokován `//www.instagram.com/embed.js`
   - **Kontrola**: Otevřete Developer Tools → Network tab

4. **Modul se neinicializuje**
   - **Příčina**: Příliš rychlé spuštění před načtením Instagram embedů
   - **Řešení**: Zvyšte `INSTAGRAM_READY_DELAY` v konfiguraci
   - **Kontrola**: Sledujte logy v konzoli

### Logování

Modul loguje důležité události do konzole:

```
Instagram Video Ads: Inicializace modulu...
Instagram Video Ads: Google IMA SDK je připraveno
Instagram Video Ads: Nalezeno 2 Instagram embedů
Instagram Video Ads: Embed 0 zpracován (540x608)
```

## Bezpečnost a soukromí

- Modul nesbírá žádná uživatelská data
- Komunikuje pouze s Google IMA SDK a poskytnutým VAST serverem
- Respektuje GDPR a další privacy regulace

## Licence

Tento modul je poskytován jako ukázka implementace. Pro produkční použití doporučujeme důkladné testování a případné úpravy podle specifických požadavků.

## Podpora

Pro technickou podporu nebo dotazy kontaktujte vývojový tým.
