/**
 * Instagram Video Ads Module - Jednoduch<PERSON> verze
 * 
 * Najde Instagram př<PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON> je layerem s Play tlačítkem
 * Po kliknutí přehraje video reklamu pomocí Google IMA SDK
 * Po skončení reklamy se vše odstraní
 */

(function() {
    'use strict';

    // Konfigurace
    const CONFIG = {
        VAST_TAG_URL: 'https://pubads.g.doubleclick.net/gampad/ads?iu=/21775744923/external/single_ad_samples&sz=640x480&cust_params=sample_ct%3Dlinear&ciu_szs=300x250%2C728x90&gdfp_req=1&output=vast&unviewed_position_start=1&env=vp&impl=s&correlator='
    };

    let processedEmbeds = [];

    /**
     * Hlavní funkce - najde Instagram embedy a přidá overlay
     */
    function init() {
        console.log('Instagram Video Ads: Spouštění...');
        
        // Čekáme na Google IMA SDK
        if (!window.google || !window.google.ima) {
            console.log('Instagram Video Ads: Čekám na Google IMA SDK...');
            setTimeout(init, 1000);
            return;
        }
        
        // Hledáme Instagram embedy
        findAndProcessInstagramEmbeds();
        
        // Opakujeme každých 3 sekundy pro nově načtené embedy
        setInterval(findAndProcessInstagramEmbeds, 3000);
    }

    /**
     * Najde a zpracuje Instagram embedy
     */
    function findAndProcessInstagramEmbeds() {
        // Hledáme všechny Instagram embedy
        const embeds = document.querySelectorAll('blockquote.instagram-media');
        
        embeds.forEach((embed, index) => {
            // Přeskočíme už zpracované embedy
            if (processedEmbeds.includes(embed)) {
                return;
            }
            
            // Zkontrolujeme, zda má embed rozměry
            const rect = embed.getBoundingClientRect();
            if (rect.width > 100 && rect.height > 100) {
                console.log(`Instagram Video Ads: Zpracovávám embed ${index} (${Math.round(rect.width)}x${Math.round(rect.height)}px)`);
                addOverlayToEmbed(embed);
                processedEmbeds.push(embed);
            }
        });
    }

    /**
     * Přidá overlay s Play tlačítkem na Instagram embed
     */
    function addOverlayToEmbed(embed) {
        // Nastavíme position: relative pro embed
        embed.style.position = 'relative';
        
        // Vytvoříme overlay
        const overlay = document.createElement('div');
        overlay.className = 'instagram-ad-overlay';
        overlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            cursor: pointer;
            border-radius: 3px;
        `;
        
        // Vytvoříme Play tlačítko
        const playButton = document.createElement('div');
        playButton.style.cssText = `
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        `;
        
        // Play ikona
        playButton.innerHTML = `
            <div style="
                width: 0;
                height: 0;
                border-left: 20px solid white;
                border-top: 12px solid transparent;
                border-bottom: 12px solid transparent;
                margin-left: 4px;
            "></div>
        `;
        
        // Hover efekt
        playButton.addEventListener('mouseenter', () => {
            playButton.style.transform = 'scale(1.1)';
        });
        playButton.addEventListener('mouseleave', () => {
            playButton.style.transform = 'scale(1)';
        });
        
        overlay.appendChild(playButton);
        
        // Kliknutí na overlay
        overlay.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            playVideoAd(embed, overlay);
        });
        
        // Přidáme overlay do embedu
        embed.appendChild(overlay);
        
        console.log('Instagram Video Ads: Overlay přidán');
    }

    /**
     * Přehraje video reklamu
     */
    function playVideoAd(embed, overlay) {
        console.log('Instagram Video Ads: Spouštím reklamu...');
        
        // Získáme rozměry embedu
        const rect = embed.getBoundingClientRect();
        const width = Math.round(rect.width);
        const height = Math.round(rect.height);
        
        // Vytvoříme kontejner pro reklamu
        const adContainer = document.createElement('div');
        adContainer.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            z-index: 1001;
            border-radius: 3px;
        `;
        
        // Vytvoříme video element
        const videoElement = document.createElement('video');
        videoElement.style.cssText = `
            width: 100%;
            height: 100%;
            object-fit: contain;
        `;
        videoElement.setAttribute('playsinline', '');
        
        adContainer.appendChild(videoElement);
        embed.appendChild(adContainer);
        
        // Skryjeme overlay
        overlay.style.display = 'none';
        
        // Inicializujeme Google IMA
        try {
            const adDisplayContainer = new google.ima.AdDisplayContainer(adContainer, videoElement);
            const adsLoader = new google.ima.AdsLoader(adDisplayContainer);
            
            // Event listenery
            adsLoader.addEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED, (event) => {
                console.log('Instagram Video Ads: Reklama načtena');
                
                const adsManager = event.getAdsManager(videoElement);
                
                // Události reklamy
                adsManager.addEventListener(google.ima.AdEvent.Type.ALL_ADS_COMPLETED, () => {
                    console.log('Instagram Video Ads: Reklama dokončena');
                    cleanupAd(embed, overlay, adContainer, adsManager);
                });
                
                adsManager.addEventListener(google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED, () => {
                    console.log('Instagram Video Ads: Požadavek na obnovení obsahu');
                    cleanupAd(embed, overlay, adContainer, adsManager);
                });
                
                adsManager.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR, (errorEvent) => {
                    console.error('Instagram Video Ads: Chyba reklamy:', errorEvent.getError());
                    cleanupAd(embed, overlay, adContainer, adsManager);
                });
                
                // Spustíme reklamu
                adDisplayContainer.initialize();
                adsManager.init(width, height, google.ima.ViewMode.NORMAL);
                adsManager.start();
            });
            
            adsLoader.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR, (errorEvent) => {
                console.error('Instagram Video Ads: Chyba při načítání reklamy:', errorEvent.getError());
                cleanupAd(embed, overlay, adContainer, null);
            });
            
            // Vytvoříme požadavek na reklamu
            const adsRequest = new google.ima.AdsRequest();
            adsRequest.adTagUrl = CONFIG.VAST_TAG_URL + Date.now();
            adsRequest.linearAdSlotWidth = width;
            adsRequest.linearAdSlotHeight = height;
            
            // Požádáme o reklamu
            adsLoader.requestAds(adsRequest);
            
        } catch (error) {
            console.error('Instagram Video Ads: Chyba při inicializaci IMA:', error);
            cleanupAd(embed, overlay, adContainer, null);
        }
    }

    /**
     * Vyčistí reklamu a odstraní vše
     */
    function cleanupAd(embed, overlay, adContainer, adsManager) {
        console.log('Instagram Video Ads: Ukončování reklamy...');
        
        // Zničíme AdsManager
        if (adsManager) {
            adsManager.destroy();
        }
        
        // Odstraníme ad kontejner
        if (adContainer) {
            adContainer.remove();
        }
        
        // Odstraníme overlay úplně
        if (overlay) {
            overlay.remove();
        }
        
        console.log('Instagram Video Ads: Vše vyčištěno - uživatel může prohlížet původní obsah');
    }

    // Spustíme po načtení stránky
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => setTimeout(init, 1000));
    } else {
        setTimeout(init, 1000);
    }

    // Export pro debugging
    window.InstagramVideoAds = {
        getProcessedEmbeds: () => processedEmbeds,
        getConfig: () => CONFIG,
        reinitialize: init
    };

})();
