/**
 * Pokročilá konfigurace pro Instagram Video Ads Module
 * 
 * Tento soubor obsahuje příklady různých konfigurací a rozšíření
 * základního modulu pro specifické případy použití.
 */

// Příklad 1: Konfigurace s vlastním VAST serverem
const CUSTOM_CONFIG = {
    VAST_TAG_URL: 'https://your-ad-server.com/vast?placement=instagram&correlator=',
    POLLING_INTERVAL: 50,           // Rychlejší polling
    MAX_POLLING_ATTEMPTS: 100,      // Více pokusů
    EMBED_READY_DELAY: 2000,        // Delší čekání na embed
    
    // Rozšířené možnosti
    AUTO_PLAY: false,               // Automatické přehrání bez kliknutí
    SKIP_BUTTON_DELAY: 5000,        // Zpoždění skip tlačítka (ms)
    MAX_AD_DURATION: 30000,         // Maximální délka reklamy (ms)
    RETRY_ON_ERROR: true,           // Opakování při chybě
    MAX_RETRIES: 2                  // Maximální počet opakování
};

// Příklad 2: Konfigurace pro mobilní zařízení
const MOBILE_CONFIG = {
    VAST_TAG_URL: 'https://mobile-ads.example.com/vast?device=mobile&correlator=',
    POLLING_INTERVAL: 200,          // Pomalejší polling pro mobilní
    EMBED_READY_DELAY: 3000,        // Delší čekání na mobilních zařízeních
    
    // Mobilní specifické nastavení
    TOUCH_FRIENDLY: true,           // Větší touch targety
    FULLSCREEN_ON_MOBILE: true,     // Fullscreen na mobilních zařízeních
    PRELOAD_ADS: false              // Nepreloadovat na mobilních sítích
};

// Příklad 3: A/B testing konfigurace
const AB_TEST_CONFIG = {
    // Varianta A - standardní reklamy
    VARIANT_A: {
        VAST_TAG_URL: 'https://ads.example.com/vast?variant=a&correlator=',
        OVERLAY_STYLE: 'standard',
        PLAY_BUTTON_SIZE: 80
    },
    
    // Varianta B - prémiové reklamy
    VARIANT_B: {
        VAST_TAG_URL: 'https://ads.example.com/vast?variant=b&correlator=',
        OVERLAY_STYLE: 'premium',
        PLAY_BUTTON_SIZE: 100
    }
};

// Příklad 4: Rozšířené event handling
const EXTENDED_EVENTS = {
    onAdStart: (embedData) => {
        console.log('Reklama spuštěna:', embedData.index);
        // Analytics tracking
        if (window.gtag) {
            gtag('event', 'ad_start', {
                'custom_parameter': embedData.index
            });
        }
    },
    
    onAdComplete: (embedData) => {
        console.log('Reklama dokončena:', embedData.index);
        // Analytics tracking
        if (window.gtag) {
            gtag('event', 'ad_complete', {
                'custom_parameter': embedData.index
            });
        }
    },
    
    onAdError: (embedData, error) => {
        console.error('Chyba reklamy:', error);
        // Error reporting
        if (window.Sentry) {
            Sentry.captureException(error);
        }
    },
    
    onOverlayClick: (embedData) => {
        console.log('Kliknutí na overlay:', embedData.index);
        // User interaction tracking
    }
};

// Příklad 5: Podmíněné načítání reklam
const CONDITIONAL_LOADING = {
    // Načíst reklamy pouze pro určité embedy
    shouldLoadAd: (embedElement) => {
        // Příklad: pouze embedy s určitým data atributem
        return embedElement.hasAttribute('data-enable-ads');
    },
    
    // Načíst reklamy pouze pro určité uživatele
    shouldLoadForUser: () => {
        // Příklad: pouze pro nepřihlášené uživatele
        return !document.cookie.includes('user_logged_in=true');
    },
    
    // Načíst reklamy pouze v určitých časech
    shouldLoadByTime: () => {
        const hour = new Date().getHours();
        return hour >= 9 && hour <= 21; // Pouze mezi 9:00 a 21:00
    }
};

// Příklad 6: Pokročilé styling možnosti
const ADVANCED_STYLING = {
    // Různé styly overlay podle pozice na stránce
    getOverlayStyle: (embedData) => {
        const rect = embedData.element.getBoundingClientRect();
        const isAboveFold = rect.top < window.innerHeight;
        
        return isAboveFold ? 'premium-overlay' : 'standard-overlay';
    },
    
    // Dynamické velikosti play tlačítka
    getPlayButtonSize: (embedData) => {
        const area = embedData.width * embedData.height;
        if (area > 300000) return 100;      // Velké embedy
        if (area > 150000) return 80;       // Střední embedy
        return 60;                          // Malé embedy
    },
    
    // Adaptivní barvy podle obsahu stránky
    getThemeColors: () => {
        const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
        return isDarkMode ? 'dark-theme' : 'light-theme';
    }
};

// Příklad 7: Integrace s dalšími systémy
const INTEGRATIONS = {
    // Google Analytics 4
    ga4Integration: {
        trackAdView: (embedData) => {
            if (window.gtag) {
                gtag('event', 'ad_impression', {
                    'event_category': 'instagram_ads',
                    'event_label': `embed_${embedData.index}`,
                    'value': 1
                });
            }
        },
        
        trackAdClick: (embedData) => {
            if (window.gtag) {
                gtag('event', 'ad_click', {
                    'event_category': 'instagram_ads',
                    'event_label': `embed_${embedData.index}`,
                    'value': 1
                });
            }
        }
    },
    
    // Facebook Pixel
    facebookPixelIntegration: {
        trackAdView: (embedData) => {
            if (window.fbq) {
                fbq('track', 'ViewContent', {
                    content_type: 'instagram_ad',
                    content_ids: [`embed_${embedData.index}`]
                });
            }
        }
    },
    
    // Custom API reporting
    apiReporting: {
        reportAdEvent: async (eventType, embedData) => {
            try {
                await fetch('/api/ad-events', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        event: eventType,
                        embed_index: embedData.index,
                        timestamp: Date.now(),
                        user_agent: navigator.userAgent
                    })
                });
            } catch (error) {
                console.error('Chyba při reportování:', error);
            }
        }
    }
};

// Příklad 8: Výkonnostní optimalizace
const PERFORMANCE_CONFIG = {
    // Lazy loading reklam
    LAZY_LOAD: true,
    LAZY_LOAD_THRESHOLD: 200,       // px od viewportu
    
    // Preloading strategií
    PRELOAD_STRATEGY: 'viewport',   // 'none', 'viewport', 'all'
    PRELOAD_COUNT: 2,               // Počet reklam k preloadování
    
    // Memory management
    CLEANUP_DELAY: 5000,            // Zpoždění před cleanup (ms)
    MAX_CONCURRENT_ADS: 1,          // Maximální počet současných reklam
    
    // Network optimizations
    CONNECTION_TIMEOUT: 10000,      // Timeout pro síťové požadavky
    RETRY_DELAY: 2000              // Zpoždění mezi pokusy
};

// Export konfigurací pro použití
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CUSTOM_CONFIG,
        MOBILE_CONFIG,
        AB_TEST_CONFIG,
        EXTENDED_EVENTS,
        CONDITIONAL_LOADING,
        ADVANCED_STYLING,
        INTEGRATIONS,
        PERFORMANCE_CONFIG
    };
}

// Globální dostupnost pro browser
if (typeof window !== 'undefined') {
    window.InstagramAdsConfigs = {
        CUSTOM_CONFIG,
        MOBILE_CONFIG,
        AB_TEST_CONFIG,
        EXTENDED_EVENTS,
        CONDITIONAL_LOADING,
        ADVANCED_STYLING,
        INTEGRATIONS,
        PERFORMANCE_CONFIG
    };
}
