<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Video Ads - Funkční test</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-info {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .test-info h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .debug-panel {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background: #4caf50; }
        .status-error { background: #f44336; }
        .status-warning { background: #ff9800; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Instagram Video Ads - Funkční test</h1>
        
        <div class="test-info">
            <h3>✅ Tento test by měl fungovat!</h3>
            <p>Tato stránka obsahuje přímo <code>blockquote.instagram-media</code> elementy bez závislosti na Instagram embed.js</p>
            <p><strong>Očekávané chování:</strong> Měli byste vidět Play tlačítka na Instagram embedech níže.</p>
        </div>

        <!-- Status panel -->
        <div style="background: white; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #ddd;">
            <h3>Status:</h3>
            <div id="statusPanel">
                <div><span class="status-indicator status-warning"></span>Načítání...</div>
            </div>
        </div>

        <!-- První Instagram embed -->
        <div class="embed-container">
            <h2>První Instagram příspěvek</h2>
            <blockquote class="instagram-media" data-instgrm-captioned data-instgrm-permalink="https://www.instagram.com/p/DKMANWSMaeq/?utm_source=ig_embed&utm_campaign=loading" data-instgrm-version="14" style="background:#FFF; border:0; border-radius:3px; box-shadow:0 0 1px 0 rgba(0,0,0,0.5),0 1px 10px 0 rgba(0,0,0,0.15); margin: 1px; max-width:540px; min-width:326px; padding:0; width:99.375%; width:-webkit-calc(100% - 2px); width:calc(100% - 2px); height:600px;">
                <div style="padding:16px; height:100%; display:flex; align-items:center; justify-content:center; background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%); color:white; font-family:Arial,sans-serif;">
                    <div style="text-align:center;">
                        <div style="font-size:48px; margin-bottom:20px;">📸</div>
                        <div style="font-size:24px; font-weight:bold; margin-bottom:10px;">Instagram Post</div>
                        <div style="font-size:16px; opacity:0.9;">@michaela.kacena</div>
                        <div style="font-size:14px; opacity:0.7; margin-top:10px;">Klikněte na Play tlačítko pro reklamu</div>
                    </div>
                </div>
            </blockquote>
        </div>

        <!-- Druhý Instagram embed -->
        <div class="embed-container">
            <h2>Druhý Instagram příspěvek</h2>
            <blockquote class="instagram-media" data-instgrm-captioned data-instgrm-permalink="https://www.instagram.com/p/C88RhSLLGXF/?utm_source=ig_embed&utm_campaign=loading" data-instgrm-version="14" style="background:#FFF; border:0; border-radius:3px; box-shadow:0 0 1px 0 rgba(0,0,0,0.5),0 1px 10px 0 rgba(0,0,0,0.15); margin: 1px; max-width:540px; min-width:326px; padding:0; width:99.375%; width:-webkit-calc(100% - 2px); width:calc(100% - 2px); height:600px;">
                <div style="padding:16px; height:100%; display:flex; align-items:center; justify-content:center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color:white; font-family:Arial,sans-serif;">
                    <div style="text-align:center;">
                        <div style="font-size:48px; margin-bottom:20px;">🎬</div>
                        <div style="font-size:24px; font-weight:bold; margin-bottom:10px;">Instagram Video</div>
                        <div style="font-size:16px; opacity:0.9;">@albatrosmedia</div>
                        <div style="font-size:14px; opacity:0.7; margin-top:10px;">Klikněte na Play tlačítko pro reklamu</div>
                    </div>
                </div>
            </blockquote>
        </div>

        <!-- Debug panel -->
        <div class="debug-panel">
            <h3>Debug log:</h3>
            <div id="debugLog">Inicializace...</div>
        </div>
    </div>

    <!-- Skripty -->
    <script src="//imasdk.googleapis.com/js/sdkloader/ima3.js"></script>
    <script src="instagram-video-ads.js"></script>
    
    <script>
        const statusPanel = document.getElementById('statusPanel');
        const debugLog = document.getElementById('debugLog');

        function updateStatus() {
            let status = '';
            
            // IMA SDK
            if (window.google && window.google.ima) {
                status += '<div><span class="status-indicator status-ok"></span>Google IMA SDK: Dostupné</div>';
            } else {
                status += '<div><span class="status-indicator status-error"></span>Google IMA SDK: Nedostupné</div>';
            }

            // Instagram embedy
            const embeds = document.querySelectorAll('blockquote.instagram-media');
            if (embeds.length > 0) {
                status += `<div><span class="status-indicator status-ok"></span>Instagram embedy: ${embeds.length} nalezeno</div>`;
            } else {
                status += '<div><span class="status-indicator status-error"></span>Instagram embedy: Žádné nenalezeny</div>';
            }

            // Video Ads Module
            if (window.InstagramVideoAds) {
                const moduleEmbeds = window.InstagramVideoAds.getEmbeds();
                status += `<div><span class="status-indicator status-ok"></span>Video Ads Module: ${moduleEmbeds.length} embedů zpracováno</div>`;
                
                // Overlay status
                let overlaysCount = 0;
                moduleEmbeds.forEach(embed => {
                    if (embed.overlay) overlaysCount++;
                });
                
                if (overlaysCount > 0) {
                    status += `<div><span class="status-indicator status-ok"></span>Play tlačítka: ${overlaysCount} zobrazeno</div>`;
                } else {
                    status += '<div><span class="status-indicator status-warning"></span>Play tlačítka: Žádná nezobrazena</div>';
                }
            } else {
                status += '<div><span class="status-indicator status-error"></span>Video Ads Module: Nenačten</div>';
            }

            statusPanel.innerHTML = status;
        }

        // Zachytávání console logů
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function addToDebugLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#f44336' : type === 'warn' ? '#ff9800' : '#333';
            logEntry.textContent = `[${timestamp}] ${message}`;
            debugLog.appendChild(logEntry);
            debugLog.scrollTop = debugLog.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            if (args[0] && args[0].includes && args[0].includes('Instagram Video Ads:')) {
                addToDebugLog(args[0], 'info');
            }
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            if (args[0] && args[0].includes && args[0].includes('Instagram Video Ads:')) {
                addToDebugLog(args[0], 'error');
            }
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            if (args[0] && args[0].includes && args[0].includes('Instagram Video Ads:')) {
                addToDebugLog(args[0], 'warn');
            }
        };

        // Pravidelné aktualizace statusu
        setInterval(updateStatus, 1000);
        
        // První aktualizace
        setTimeout(updateStatus, 500);
    </script>
</body>
</html>
