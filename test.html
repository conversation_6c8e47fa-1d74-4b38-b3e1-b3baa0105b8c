<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Video Ads - <PERSON><PERSON><PERSON><PERSON>ý test</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-info {
            background: #f0f8ff;
            border: 1px solid #0066cc;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .test-info h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .debug-panel {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .debug-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .debug-button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Instagram Video Ads - Test</h1>
        
        <div class="test-info">
            <h3>Instrukce pro testování:</h3>
            <ol>
                <li>Počkejte na načtení stránky (cca 2-3 sekundy)</li>
                <li>Měli byste vidět Play tlačítka na Instagram embedech</li>
                <li>Klikněte na Play tlačítko pro spuštění reklamy</li>
                <li>Po dokončení reklamy se vrátí původní obsah</li>
            </ol>
        </div>

        <!-- Jednoduchý Instagram embed pro test -->
        <div class="embed-container">
            <h2>Test Instagram embed</h2>
            <blockquote class="instagram-media" data-instgrm-captioned data-instgrm-permalink="https://www.instagram.com/p/test123/" data-instgrm-version="14" style="background:#FFF; border:0; border-radius:3px; box-shadow:0 0 1px 0 rgba(0,0,0,0.5),0 1px 10px 0 rgba(0,0,0,0.15); margin: 1px; max-width:540px; min-width:326px; padding:0; width:99.375%; width:-webkit-calc(100% - 2px); width:calc(100% - 2px);">
                <div style="padding:16px;">
                    <div style="display: flex; flex-direction: row; align-items: center;">
                        <div style="background-color: #F4F4F4; border-radius: 50%; flex-grow: 0; height: 40px; margin-right: 14px; width: 40px;"></div>
                        <div style="display: flex; flex-direction: column; flex-grow: 1; justify-content: center;">
                            <div style="background-color: #F4F4F4; border-radius: 4px; flex-grow: 0; height: 14px; margin-bottom: 6px; width: 100px;"></div>
                            <div style="background-color: #F4F4F4; border-radius: 4px; flex-grow: 0; height: 14px; width: 60px;"></div>
                        </div>
                    </div>
                    <div style="padding: 19% 0;"></div>
                    <div style="display:block; height:50px; margin:0 auto 12px; width:50px; background: #f0f0f0; border-radius: 50%;"></div>
                    <div style="padding-top: 8px;">
                        <div style="color:#3897f0; font-family:Arial,sans-serif; font-size:14px; font-style:normal; font-weight:550; line-height:18px;">
                            Test Instagram příspěvek
                        </div>
                    </div>
                    <div style="padding: 12.5% 0;"></div>
                    <p style="color:#c9c8cd; font-family:Arial,sans-serif; font-size:14px; line-height:17px; margin-bottom:0; margin-top:8px; overflow:hidden; padding:8px 0 7px; text-align:center; text-overflow:ellipsis; white-space:nowrap;">
                        Testovací příspěvek pro video reklamy
                    </p>
                </div>
            </blockquote>
        </div>

        <!-- Debug panel -->
        <div class="debug-panel">
            <h3>Debug panel:</h3>
            <button class="debug-button" onclick="showDebugInfo()">Zobrazit debug info</button>
            <button class="debug-button" onclick="reinitialize()">Reinicializovat</button>
            <button class="debug-button" onclick="checkIMA()">Zkontrolovat IMA SDK</button>
            <div id="debug-output" style="margin-top: 10px; white-space: pre-wrap;"></div>
        </div>
    </div>

    <!-- Skripty -->
    <script src="//imasdk.googleapis.com/js/sdkloader/ima3.js"></script>
    <script src="instagram-video-ads.js"></script>
    
    <script>
        // Debug funkce
        function showDebugInfo() {
            const output = document.getElementById('debug-output');
            if (window.InstagramVideoAds) {
                const embeds = window.InstagramVideoAds.getEmbeds();
                const config = window.InstagramVideoAds.getConfig();
                
                output.textContent = `
Počet detekovaných embedů: ${embeds.length}
Konfigurace: ${JSON.stringify(config, null, 2)}
Embedy: ${JSON.stringify(embeds.map(e => ({
    index: e.index,
    width: e.width,
    height: e.height,
    hasOverlay: !!e.overlay
})), null, 2)}
                `;
            } else {
                output.textContent = 'InstagramVideoAds není dostupné';
            }
        }

        function reinitialize() {
            if (window.InstagramVideoAds) {
                window.InstagramVideoAds.reinitialize();
                document.getElementById('debug-output').textContent = 'Modul reinicializován';
            }
        }

        function checkIMA() {
            const output = document.getElementById('debug-output');
            if (window.google && window.google.ima) {
                output.textContent = 'Google IMA SDK je dostupné ✓';
            } else {
                output.textContent = 'Google IMA SDK není dostupné ✗';
            }
        }

        // Automatické zobrazení debug info po načtení
        window.addEventListener('load', () => {
            setTimeout(() => {
                showDebugInfo();
            }, 3000);
        });
    </script>
</body>
</html>
